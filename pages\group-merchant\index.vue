<template>
  <snowy-layout title="集团商户" :isTabbar="false" :isFirstPage="true">
    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <uni-icons type="search" size="18" color="#999" class="search-icon"></uni-icons>
        <input
          class="search-input"
          placeholder="可输入姓名、证件号码、手机号进行查询"
          v-model="searchKey"
          @input="onSearchInput"
        />
      </view>
    </view>

    <!-- 商户列表 -->
    <view class="merchant-list">
      <view 
        class="merchant-item" 
        v-for="(item, index) in merchantList" 
        :key="index"
        @click="viewMerchantDetail(item)"
      >
        <view class="merchant-header">
          <view class="merchant-name">{{ item.name || '我是商户名称' }}</view>
          <view class="merchant-action">
            <view class="action-btn">商户看板</view>
          </view>
        </view>
        
        <view class="merchant-info">
          <view class="info-row">
            <view class="info-label">法人名称</view>
            <view class="info-value">{{ item.legalPersonName || '张三' }}</view>
          </view>
          
          <view class="info-row">
            <view class="info-label">法人手机号</view>
            <view class="info-value">{{ item.legalPersonPhone || '13562356235' }}</view>
          </view>
          
          <view class="info-row">
            <view class="info-label">办公地址</view>
            <view class="info-value">{{ item.officeAddress || '重庆市九龙坡' }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMore">
      <view v-if="loading" class="loading-text">加载中...</view>
      <view v-else @click="loadMore" class="load-more-text">点击加载更多</view>
    </view>
    
    <!-- 没有更多数据 -->
    <view class="no-more" v-if="!hasMore && merchantList.length > 0">
      没有更多数据了
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-if="!loading && merchantList.length === 0">
      <view class="empty-text">暂无商户数据</view>
    </view>
  </snowy-layout>
</template>

<script>
import bizGroupMerchantApi from '@/api/biz/bizGroupMerchantApi'
import store from '@/store'

export default {
  data() {
    return {
      searchKey: '',
      merchantList: [],
      loading: false,
      hasMore: true,
      current: 1,
      size: 10,
      searchTimer: null
    }
  },
  
  onLoad() {
    // 隐藏tabbar
    uni.hideTabBar()
    this.loadMerchantList()
  },
  
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },
  
  onPullDownRefresh() {
    this.refreshList()
  },
  
  methods: {
    /**
     * 加载商户列表
     */
    async loadMerchantList(isRefresh = false) {
      if (this.loading) return
      
      this.loading = true
      
      try {
        const params = {
          parentMerchantId: store.getters.entityId, // 取当前用户的entityId作为集团商户ID
          current: isRefresh ? 1 : this.current,
          size: this.size
        }
        
        if (this.searchKey.trim()) {
          params.searchKey = this.searchKey.trim()
        }
        
        const res = await bizGroupMerchantApi.getGroupMerchantList(params)
        
        if (res.success) {
          const newList = res.data.records || []
          
          if (isRefresh) {
            this.merchantList = newList
            this.current = 1
          } else {
            this.merchantList = [...this.merchantList, ...newList]
          }
          
          // 判断是否还有更多数据
          this.hasMore = newList.length === this.size
          
          if (!isRefresh) {
            this.current++
          }
        } else {
          uni.showToast({
            title: res.message || '获取商户列表失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取商户列表失败:', error)
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
        if (isRefresh) {
          uni.stopPullDownRefresh()
        }
      }
    },
    
    /**
     * 搜索输入处理
     */
    onSearchInput() {
      // 防抖处理
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      
      this.searchTimer = setTimeout(() => {
        this.refreshList()
      }, 500)
    },
    
    /**
     * 刷新列表
     */
    refreshList() {
      this.current = 1
      this.hasMore = true
      this.loadMerchantList(true)
    },
    
    /**
     * 加载更多
     */
    loadMore() {
      this.loadMerchantList()
    },
    
    /**
     * 查看商户详情/商户看板
     */
    viewMerchantDetail(merchant) {
      // 这里可以跳转到商户详情页面或商户看板
      uni.showToast({
        title: `查看${merchant.name || '商户'}详情`,
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.search-container {
  padding: 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #f5f5f5;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 48rpx;
  padding: 20rpx 32rpx;
  
  .search-icon {
    color: #999;
    margin-right: 16rpx;
    font-size: 32rpx;
  }
  
  .search-input {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    
    &::placeholder {
      color: #999;
    }
  }
}

.merchant-list {
  padding: 0 32rpx;
}

.merchant-item {
  background: #fff;
  border-radius: 16rpx;
  margin: 24rpx 0;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.merchant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  
  .merchant-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
  
  .merchant-action {
    .action-btn {
      background: #FFD700;
      color: #333;
      padding: 12rpx 24rpx;
      border-radius: 24rpx;
      font-size: 24rpx;
      font-weight: 500;
    }
  }
}

.merchant-info {
  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .info-label {
      font-size: 28rpx;
      color: #666;
    }
    
    .info-value {
      font-size: 28rpx;
      color: #333;
      text-align: right;
      flex: 1;
      margin-left: 32rpx;
    }
  }
}

.load-more {
  padding: 40rpx;
  text-align: center;

  .loading-text,
  .load-more-text {
    color: #666;
    font-size: 28rpx;
  }
}

.no-more {
  padding: 40rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  padding: 120rpx 40rpx;
  text-align: center;
  
  .empty-text {
    color: #999;
    font-size: 28rpx;
  }
}
</style>
