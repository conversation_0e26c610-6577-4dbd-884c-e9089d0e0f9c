import store from '@/store'
import config from '@/config'
import userIdentity from '@/utils/user-identity'

import bizMiniApi from '@/api/biz/bizMiniApi'
import loginApi from '@/api/auth/login-api'
const TokenKey = 'App-Token'
export function getToken () {
  return uni.getStorageSync(TokenKey)
}
export function setToken (token) {
  return uni.setStorageSync(TokenKey, token)
}
export function removeToken () {
  return uni.removeStorageSync(TokenKey)
}
// 从服务器获取菜单数据
export const fetchMenus = async (cardId) => {
  try {
    console.log(userIdentity.getIdentityText(), '当前身份');

    // 根据用户身份返回不同的菜单
    return getMenusByUserIdentity()
  } catch (error) {
    console.error('获取菜单失败:', error)
    // 返回默认菜单作为备用
    return getMenusByUserIdentity()
  }
}

// 根据用户身份获取菜单
export const getMenusByUserIdentity = () => {
  // 判断用户身份
  if (userIdentity.isPartner()) {
    // 合伙人菜单
    return getPartnerMenus()
  } else if (userIdentity.isMerchantAdmin()) {
    // 商户管理员菜单
    return getMerchantMenus()
  } else if (userIdentity.isStaff()&& (userIdentity.isStaffMERAdmin()|| userIdentity.isStaffDEPTAdmin())) {
    // 员工 管理员和部门负责人菜单
    return getMerchantMenus()
  } else if (userIdentity.isStaff()) {
    // 员工菜单
    return getStaffMenus()
  } else if (userIdentity.isGroupMerchant()) {
    // 集团商户菜单
    return getGroupMerchantMenus()
  } else {
    // 默认菜单（员工、推荐官等）
    return getDefaultMenus()
  }
}
export const getStaffMenus = () => {
  return [
    {
      path: '/pages/my-clue/index',
      text: '我的线索',
      icon: '/static/images/tabbar/xs.png',
      activeIcon: '/static/images/tabbar/xs-active.png',
      extJson: '[]',
      isTabbar: true
    },
    {
      path: '/pages/merchant-board/index',
      text: '商户看板',
      icon: '/static/images/tabbar/shkb.png',
      activeIcon: '/static/images/tabbar/shkb-active.png',
      extJson: '[]',
      isTabbar: true
    },
    {
      path: '/pages/visit-record/index',
      text: '访问记录',
      icon: '/static/images/tabbar/fwjl.png',
      activeIcon: '/static/images/tabbar/fwjl-active.png',
      extJson: '[]',
      isTabbar: true
    },
    {
      path: '/pages/mine/index',
      text: '我的',
      icon: '/static/images/tabbar/my.png',
      activeIcon: '/static/images/tabbar/my-active.png',
      extJson: '[]',
      isTabbar: true
    }
  ]
}
// 商户管理员菜单
export const getMerchantMenus = () => {
  return [
    {
      path: '/pages/my-clue/index',
      text: '我的线索',
      icon: '/static/images/tabbar/xs.png',
      activeIcon: '/static/images/tabbar/xs-active.png',
      extJson: '[]',
      isTabbar: true
    },
    {
      path: '/pages/team-clue/index',
      text: '团队线索',
      icon: '/static/images/tabbar/tdxs.png',
      activeIcon: '/static/images/tabbar/tdxs-active.png',
      extJson: '[]',
      isTabbar: true
    },
    {
      path: '/pages/merchant-board/index',
      text: '商户看板',
      icon: '/static/images/tabbar/shkb.png',
      activeIcon: '/static/images/tabbar/shkb-active.png',
      extJson: '[]',
      isTabbar: true
    },
    {
      path: '/pages/visit-record/index',
      text: '访问记录',
      icon: '/static/images/tabbar/fwjl.png',
      activeIcon: '/static/images/tabbar/fwjl-active.png',
      extJson: '[]',
      isTabbar: true
    },
    {
      path: '/pages/mine/index',
      text: '我的',
      icon: '/static/images/tabbar/my.png',
      activeIcon: '/static/images/tabbar/my-active.png',
      extJson: '[]',
      isTabbar: true
    }
  ]
}

// 合伙人菜单
export const getPartnerMenus = () => {
  return [
    {
      path: '/partner/home/<USER>',
      text: '首页',
      icon: '/static/images/tabbar/partner-home.png',
      activeIcon: '/static/images/tabbar/partner-home-active.png',
      extJson: '[]',
      isTabbar: false
    },
    {
      path: '/partner/mine/index',
      text: '我的',
      icon: '/static/images/tabbar/partner-my.png',
      activeIcon: '/static/images/tabbar/partner-my-active.png',
      extJson: '[]',
      isTabbar: false
    }
  ]
}
export const getGroupMerchantMenus = () => {
  return [
   
  ]
}
// 默认菜单（员工、推荐官等）
export const getDefaultMenus = () => {
  return [
    {
      path: '/pages/my-clue/index',
      text: '我的线索',
      icon: '/static/images/tabbar/xs.png',
      activeIcon: '/static/images/tabbar/xs-active.png',
      extJson: '[]',
      isTabbar: true
    },
    {
      path: '/pages/mine/index',
      text: '我的',
      icon: '/static/images/tabbar/my.png',
      activeIcon: '/static/images/tabbar/my-active.png',
      extJson: '[]',
      isTabbar: true
    }
  ]
}
// 应用初始化
export const initializeApp = async () => {
  try {
    // 获取菜单数据
    let menus = await fetchMenus()
    if(menus.length){
      // 设置当前菜单和菜单列表
      store.commit('SET_currentMenu', menus[0])
      await store.dispatch('getMenus', menus)
    }
    // 根据用户身份跳转到不同的首页
    const homePath = getHomePathByUserIdentity()
    uni.$snowy.tab.reLaunch(homePath)
  } catch (error) {
    console.error('初始化失败:', error)
    uni.showToast({
      title: '系统初始化失败，请重试',
      icon: 'none'
    })
  }
}

// 根据用户身份获取首页路径
export const getHomePathByUserIdentity = () => {
  if (userIdentity.isPartner()) {
    return '/partner/home/<USER>'
  } else if (userIdentity.isMerchantAdmin()) {
    return '/pages/my-clue/index'
  } else if (userIdentity.isGroupMerchant()) {
    return '/pages/group-merchant/index'
  }  else {
    return '/pages/my-clue/index'
  }
}

// 页面跳转 校验权限
export function checkPermission (path) {
  console.log('checkPermission', path, config.NO_TOKEN_WHITE_LIST)
  const token = uni.getStorageSync('token')
  if (!token) {
    uni.login({
      provider: 'weixin',
      success: async (res) => {
        console.log('login', res.code)
        const code = res.code
        uni.getUserInfo({
          provider: 'weixin',
          success: async (e) => {
            console.log('getUserInfo', e)
            const res = await loginApi.wxMpLogin({
              code,
              encryptedData: e.encryptedData,
              ivStr: e.iv,
              maType: 'B'
            })
            uni.setStorageSync('thirdId', res.thirdId)
            if (path === '/pages/auth/index') {
              return true
            }
            uni.$snowy.tab.reLaunch('/pages/auth/index')
            console.log('login', res)
          }
        })
      },
      fail: (err) => {
        console.log('login', err)
      }
    })
    if (path === '/pages/auth/index') {
      return true
    }
    return true
  }
  // if (token) {
  //   // 应用初始化
  //   initializeApp()
  // }
  return true
}
