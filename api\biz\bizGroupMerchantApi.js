import request from '@/utils/request'

export default {
  /**
   * 获取集团下商户列表
   * @param {Object} data - 请求参数
   * @param {string} data.parentMerchantId - 集团商户ID (取entityId)
   * @param {string} [data.searchKey] - 搜索关键字（可输入姓名、证件号码、手机号进行查询）
   * @param {number} [data.current] - 当前页码，默认1
   * @param {number} [data.size] - 每页条数，默认10
   * @returns {Promise} - 返回商户列表数据
   */
  getGroupMerchantList(data) {
    return request({
      url: '/merchant/group/mer/list',
      method: 'GET',
      data: data
    })
  }
}
