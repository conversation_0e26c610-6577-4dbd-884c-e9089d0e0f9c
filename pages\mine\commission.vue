<template>
  <snowy-layout title="我的佣金" :isTabbar="false" :isFirstPage="false">
    <view class="commission-container">
      <!-- 余额卡片 -->
      <view class="balance-card">
        <view class="balance-info">
          <view class="balance-label">余额(元)</view>
          <view class="balance-amount">{{ accountInfo.balance || '0.00' }}</view>
          <view class="withdrawn-info">已提现：{{ accountInfo.withdrawnAmount || '0.00' }}</view>
        </view>
        <view class="withdraw-btn" @click="handleWithdraw">
          余额提取
        </view>
      </view>

      <!-- 标签页 -->
      <view class="tab-container">
        <view class="tab-list">
          <view class="tab-item" :class="{ active: currentTab === 0 }" @click="switchTab(0)">
            提取记录
          </view>
          <view class="tab-item" :class="{ active: currentTab === 1 }" @click="switchTab(1)">
            佣金记录
          </view>
        </view>
      </view>

      <!-- 日期筛选 -->
      <view class="date-filter">
        <uni-datetime-picker type="daterange" :value="dateRange" start-placeholder="提取开始日" end-placeholder="提取结束日"
          rangeSeparator="-" @change="onDateRangeChange" @clear="onDateRangeClear" />
      </view>

      <!-- 列表内容 -->
      <scroll-view class="record-list" scroll-y="true" @scrolltolower="loadMore" refresher-enabled
        :refresher-triggered="refreshing" @refresherrefresh="onRefresh">
        <!-- 提取记录 -->
        <view v-if="currentTab === 0" class="withdraw-list">
          <view v-for="(item, index) in withdrawList" :key="index" class="withdraw-item">
            <view class="withdraw-info">
              <text class="withdraw-time">{{ formatDateTime(item.createTime) }}</text>
              <text class="withdraw-desc">提取佣金{{ item.amount }}元</text>
            </view>
            <view class="withdraw-status" :class="getWithdrawStatusClass(item.cashStatus)">
              {{ getStatusText(item.cashStatus) }}
            </view>
          </view>

          <view v-if="withdrawList.length === 0 && !loading" class="empty-state">
            <text>暂无提取记录</text>
          </view>
        </view>

        <!-- 佣金记录 -->
        <view v-if="currentTab === 1" class="profit-list">
          <view v-for="(item, index) in profitList" :key="index" class="commission-item">
            <!-- 任务名称 -->
            <view class="commission-title">{{ item.taskName || '我是任务名称' }}</view>

            <!-- 商户信息行 -->
            <view class="commission-row">
              <view class="commission-label">商户名称</view>
              <view class="commission-value">{{ item.merchantName || '商贸公司' }}</view>
            </view>

            <!-- 分佣类型行 -->
            <view class="commission-row">
              <view class="commission-label">分佣类型</view>
              <view class="commission-value">{{ item.profitType || '分佣' }}</view>
            </view>

            <!-- 分佣金额行 -->
            <view class="commission-row">
              <view class="commission-label">分佣金额</view>
              <view class="commission-amount">{{ item.profitAmount || '2333.00' }}</view>
            </view>

            <!-- 分佣时间行 -->
            <view class="commission-row">
              <view class="commission-label">分佣时间</view>
              <view class="commission-time">{{ formatDateTime(item.createTime) }}</view>
            </view>
          </view>

          <view v-if="profitList.length === 0 && !loading" class="empty-state">
            <text>暂无佣金记录</text>
          </view>
        </view>

        <!-- 加载状态 -->
        <view v-if="loading" class="loading-state">
          <text>加载中...</text>
        </view>

        <view v-if="noMore && (withdrawList.length > 0 || profitList.length > 0)" class="no-more">
          <text>没有更多数据了</text>
        </view>
      </scroll-view>
    </view>

  </snowy-layout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import bizCommissionApi from '@/api/biz/bizCommissionApi'

// 页面数据
const currentTab = ref(0)
const dateRange = ref([])
const startDate = ref('')
const endDate = ref('')

// 账户信息
const accountInfo = ref({
  balance: '0.00',
  withdrawnAmount: '0.00'
})

// 列表数据
const withdrawList = ref([])
const profitList = ref([])
const loading = ref(false)
const refreshing = ref(false)
const noMore = ref(false)
const page = ref(1)
const pageSize = ref(10)

// 用户信息
const userInfo = ref({
  entityId: '',
  entityType: ''
})

onLoad(() => {
  // 获取用户信息
  const user = uni.getStorageSync('roleApp')
  if (user) {
    userInfo.value.entityId = user.entityId
    userInfo.value.entityType = user.entityType || 'STAFF'
  }

  // 初始化数据
  loadAccountInfo()
  loadRecordList()
})

// 获取账户信息
const loadAccountInfo = async () => {
  try {
    const response = await bizCommissionApi.getAccountDetail({
      entityId: userInfo.value.entityId,
      entityType: userInfo.value.entityType
    })

    if (response) {
      accountInfo.value.balance = response.balance?.toFixed(2) || '0.00'
      // 计算已提现金额（这里可能需要根据实际业务逻辑调整）
      accountInfo.value.withdrawnAmount = response.frozenBalance?.toFixed(2) || '0.00'
    }
  } catch (error) {
    console.error('获取账户信息失败：', error)
  }
}

// 切换标签页
const switchTab = (index) => {
  currentTab.value = index
  resetList()
  loadRecordList()
}

// 日期范围改变
const onDateRangeChange = (e) => {
  dateRange.value = e
  if (e && e.length === 2) {
    startDate.value = e[0]
    endDate.value = e[1]
  } else {
    startDate.value = ''
    endDate.value = ''
  }
  resetList()
  loadRecordList()
}

// 清空日期范围
const onDateRangeClear = () => {
  dateRange.value = []
  startDate.value = ''
  endDate.value = ''
  resetList()
  loadRecordList()
}

// 重置列表
const resetList = () => {
  page.value = 1
  noMore.value = false
  if (currentTab.value === 0) {
    withdrawList.value = []
  } else {
    profitList.value = []
  }
}

// 加载记录列表
const loadRecordList = async () => {
  if (loading.value || noMore.value) return

  loading.value = true

  try {
    const params = {
      entityId: userInfo.value.entityId,
      entityType: userInfo.value.entityType,
      current: page.value,
      size: pageSize.value
    }

    // 添加日期筛选
    if (startDate.value) {
      params.createTimeStart = startDate.value
    }
    if (endDate.value) {
      params.createTimeEnd = endDate.value
    }
    let response
    if (currentTab.value === 0) {
      // 提取记录
      response = await bizCommissionApi.getCashList(params)
    } else {
      // 佣金记录
      response = await bizCommissionApi.getProfitOrderDetailList(params)
    }

    if (response) {
      const newData = response?.records || []

      if (currentTab.value === 0) {
        if (page.value === 1) {
          withdrawList.value = newData
        } else {
          withdrawList.value.push(...newData)
        }
      } else {
        if (page.value === 1) {
          profitList.value = newData
        } else {
          profitList.value.push(...newData)
        }
      }

      // 判断是否还有更多数据
      if (newData.length < pageSize.value) {
        noMore.value = true
      }
    }
  } catch (error) {
    console.error('获取记录列表失败：', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (!loading.value && !noMore.value) {
    page.value++
    loadRecordList()
  }
}

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true
  resetList()
  loadAccountInfo()
  loadRecordList()
}

// 余额提取
const handleWithdraw = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return '--'
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'PROCESSING':
    case 'PENDING':
      return 'status-processing'
    case 'SUCCESS':
    case 'COMPLETED':
      return 'status-success'
    case 'FAILED':
    case 'REJECTED':
      return 'status-failed'
    default:
      return 'status-default'
  }
}

// 获取提取记录状态样式类
const getWithdrawStatusClass = (status) => {
  switch (status) {
    case 0:
      return 'withdraw-status-processing'
    case 1:
      return 'withdraw-status-success'
    case 2:
      return 'withdraw-status-failed'
    case 3:
      return 'withdraw-status-cancelled'
    case 4:
      return 'withdraw-status-pending'
    default:
      return 'withdraw-status-default'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 0:
      return '处理中'
    case 1:
      return '成功'
    case 2:
      return '失败'
    case 3:
      return '已撤销'
    case 4:
      return '已出账待确认支付'
    // 兼容旧的字符串状态
    case 'PROCESSING':
    case 'PENDING':
      return '处理中'
    case 'SUCCESS':
    case 'COMPLETED':
      return '成功'
    case 'FAILED':
    case 'REJECTED':
      return '失败'
    default:
      return '未知状态'
  }
}
</script>

<style lang="scss" scoped>
.commission-container{
  height: 100%;
  display: flex;
  flex-direction: column;
}
// 余额卡片
.balance-card {
  background-image: url('https://antscard.com/minio/art-cloud-prod/static/images/yj-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  margin: 26rpx;
  padding: 40rpx;
  border-radius: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  position: relative;
  overflow: hidden;
}

.balance-info {
  flex: 1;
}

.balance-label {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 16rpx;
  font-weight: 400;
}

.balance-amount {
  font-size: 50rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.withdrawn-info {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.withdraw-btn {
  background-color: rgba(255, 255, 255, 0.9);
  color: rgba(180, 136, 56, 1);
  padding: 24rpx 32rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

// 标签页
.tab-container {
  margin: 0 20rpx;
  border-radius: 10rpx;
  padding: 10rpx;
}

.tab-list {
  display: flex;
  justify-content: space-around;
}

.tab-item {
  text-align: center;
  padding: 20rpx;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.80);
  transition: all 0.3s;
  position: relative;
  &.active {
    color: rgba(255, 209, 0, 1);
    font-weight: 500;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 30rpx;
      right: 30rpx;
      height: 4rpx;
      background-color: #ffaa00;
    }
  }
}

// 日期筛选
.date-filter {
  padding: 20rpx;
  margin: 0 26rpx;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  background: white;
  :deep(.uni-date-x--border) {
    border-radius: 40rpx !important;
    overflow: hidden;
    padding-left: 20rpx;
  }
}

// 记录列表
.record-list {
  flex: 1;
  padding: 20rpx 26rpx;
  box-sizing: border-box;
  overflow: auto;
}

// 佣金记录项样式
.commission-item {
  background-color: white;
  padding: 32rpx 24rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
}

.commission-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  line-height: 1.4;
}

.commission-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.commission-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

.commission-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
}

.commission-amount {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.commission-time {
  font-size: 28rpx;
  color: #999;
  font-weight: 400;
}
.withdraw-list{
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  overflow: auto;
}
// 提取记录样式
.withdraw-item {
  background-color: white;
  padding: 32rpx 24rpx;
  margin-bottom: 2rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
}

.withdraw-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.withdraw-time {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
  white-space: nowrap;
}

.withdraw-desc {
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.withdraw-status {
  padding: 18rpx 33rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  font-weight: 500;
  white-space: nowrap;

  &.withdraw-status-processing {
    background-color: rgba(255, 209, 0, 1);
    color: white;
  }

  &.withdraw-status-success {
    background-color: #FFF3CD;
    color: #D4AF37;
  }

  &.withdraw-status-failed {
    background-color: #f8d7da;
    color: #721c24;
  }

  &.withdraw-status-cancelled {
    background-color: #e2e3e5;
    color: #6c757d;
  }

  &.withdraw-status-pending {
    background-color: #fff3cd;
    color: #856404;
  }

  &.withdraw-status-default {
    background-color: #e2e3e5;
    color: #6c757d;
  }
}

.record-status {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;

  &.status-processing {
    background-color: #fff3cd;
    color: #856404;
  }

  &.status-success {
    background-color: #d4edda;
    color: #155724;
  }

  &.status-failed {
    background-color: #f8d7da;
    color: #721c24;
  }

  &.status-default {
    background-color: #e2e3e5;
    color: #6c757d;
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

// 加载状态
.loading-state {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}

.no-more {
  text-align: center;
  padding: 40rpx 0;
  color: #ccc;
  font-size: 24rpx;
}
</style>
